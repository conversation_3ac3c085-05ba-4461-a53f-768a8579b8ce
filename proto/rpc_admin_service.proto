syntax = "proto3";

package pb;

import "google/api/annotations.proto";
import "protoc-gen-openapiv2/options/annotations.proto";
import "rpc_dto.proto";

option go_package = "github.com/liveutil/admin_service/pb";

option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_swagger) = {
  info: {
    title: "Administration Service API";
    version: "1.0.0";
    description: "Administration Service API for close loop platform monitoring and management";
    contact: {
      name: "Administration Service API Support";
      email: "<EMAIL>";
    };
  };
  schemes: HTTP;
  schemes: HTTPS;
  consumes: "application/json";
  produces: "application/json";
  security_definitions: {
    security: {
      key: "Bearer";
      value: {
        type: TYPE_API_KEY;
        in: IN_HEADER;
        name: "Authorization";
        description: "Authentication token, prefixed by Bearer: Bearer <token>";
      };
    };
  };
  security: {
    security_requirement: {
      key: "Bearer";
      value: {};
    };
  };
};

// AdminService service provides authentication and authorization for the close loop platform
service AdminService {
  // ListUsers method returns list of users with deleted status filter applied
  rpc ListUsers(UsersListRequest) returns (UserListResponse) {
    option (google.api.http) = {
      get: "/v1/admin/users/{page_size}/{page_offset}/{deleted_filter}"
    };
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {
      summary: "List Users";
      description: "Get list of users with deleted status filter applied";
      security: {
        security_requirement: {
          key: "Bearer";
          value: {};
        };
      };
    };
  }
}