syntax = "proto3";

package pb;

import "rpc_user.proto";

option go_package = "github.com/liveutil/admin_service/pb";

// Empty message for methods that require no input
message Empty {}

// UsersListRequest message for fetching list of users with deleted filter applied
message UsersListRequest {
  // returned list page size
  int32 page_size = 1;
  // returned list page offset
  int32 page_offset = 2;
  // deleted users filter condition [deleted, not_deleted, both]
  string deleted_filter = 3;
}

// UserListResponse message for fetching list of users
message UserListResponse {
  // error sets true if processing failed otherwise sets false
  bool error = 1;
  // message contains error message if error is true or helper message if error is false
  string message = 2;
  // has more items
  bool has_more = 3 [json_name = "has_more"];
  // count of items
  int32 count = 4;
  // list of users with pagination
  repeated User users = 5 [json_name = "users"];
}