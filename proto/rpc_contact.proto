syntax = "proto3";

package pb;

import "google/protobuf/timestamp.proto";

option go_package = "github.com/liveutil/admin_service/pb";

// Contact message containing all contact data
message Contact {
  // contact public identifier to hide real database id of contact
  string identifier = 1;
  // contact mobile number
  string mobile = 2;
  // contact email address
  string email = 3;
  // contact meta data
  map<string, string> meta_data = 4;
  // contact creation time
  google.protobuf.Timestamp created_at = 5;
}