syntax = "proto3";

package pb;

import "google/protobuf/timestamp.proto";

option go_package = "github.com/liveutil/admin_service/pb";

// Profile message containing all profile data
message Profile {
  // profile public identifier to hide real database id of profile
  string identifier = 1;
  // profile type
  string profile_type = 2;
  // profile first name
  string first_name = 3;
  // profile last name
  string last_name = 4;
  // profile national id
  string national_id = 5;
  // profile status
  string status = 6;
  // profile meta data
  map<string, string> meta_data = 7;
  // profile creation time
  google.protobuf.Timestamp created_at = 8;
}