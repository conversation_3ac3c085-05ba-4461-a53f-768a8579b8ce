version: "2"
sql:
  - schema: "./schemas/"
    queries: "./queries/"
    engine: "postgresql"
    gen:
      go:
        package: "postgres"
        sql_package: "pgx/v5"
        out: "./postgres"
        emit_json_tags: true
        emit_interface: true
        emit_empty_slices: true
        overrides:
          - db_type: "timestamptz"
            go_type: "time.Time"
          - db_type: "uuid"
            go_type: "github.com/google/uuid.UUID"