// Code generated by sqlc. DO NOT EDIT.
// versions:
//   sqlc v1.29.0

package postgres

import (
	"context"
)

type Querier interface {
	CreateContact(ctx context.Context, arg CreateContactParams) (Contact, error)
	CreateUser(ctx context.Context, arg CreateUserParams) (User, error)
	CreateUserAndRelations(ctx context.Context, arg CreateUserAndRelationsParams) (CreateUserAndRelationsRow, error)
	CreateUserSession(ctx context.Context, arg CreateUserSessionParams) (Session, error)
	FindActiveEmailContact(ctx context.Context, arg FindActiveEmailContactParams) (FindActiveEmailContactRow, error)
	FindActiveMobileContact(ctx context.Context, arg FindActiveMobileContactParams) (FindActiveMobileContactRow, error)
	GetActiveUserById(ctx context.Context, id int64) (User, error)
	GetActiveUserByIdentifier(ctx context.Context, identifier string) (User, error)
	GetContactByEmail(ctx context.Context, email string) (Contact, error)
	GetContactById(ctx context.Context, id int64) (Contact, error)
	GetContactByMobile(ctx context.Context, mobile string) (Contact, error)
	GetContactByUserID(ctx context.Context, userID int64) (Contact, error)
	GetContactByUserId(ctx context.Context, userID int64) (Contact, error)
	GetProfileByUserID(ctx context.Context, userID int64) (Profile, error)
	GetSafeUserById(ctx context.Context, id int64) (GetSafeUserByIdRow, error)
	GetSessionById(ctx context.Context, arg GetSessionByIdParams) (Session, error)
	GetUserById(ctx context.Context, id int64) (User, error)
	GetUserByIdentifier(ctx context.Context, identifier string) (User, error)
	ListAllUsers(ctx context.Context, arg ListAllUsersParams) ([]User, error)
	ListDeletedUsers(ctx context.Context, arg ListDeletedUsersParams) ([]User, error)
	ListNotDeletedUsers(ctx context.Context, arg ListNotDeletedUsersParams) ([]User, error)
	ListUsersWithProfileAndContact(ctx context.Context, arg ListUsersWithProfileAndContactParams) ([]ListUsersWithProfileAndContactRow, error)
	RenewUserSession(ctx context.Context, arg RenewUserSessionParams) (Session, error)
	SafeDeleteContact(ctx context.Context, id int64) error
	SetEmailOTP(ctx context.Context, arg SetEmailOTPParams) (Contact, error)
	SetMobileOTP(ctx context.Context, arg SetMobileOTPParams) (Contact, error)
	UpdateContact(ctx context.Context, arg UpdateContactParams) (Contact, error)
	UpdateUser(ctx context.Context, arg UpdateUserParams) (User, error)
}

var _ Querier = (*Queries)(nil)
