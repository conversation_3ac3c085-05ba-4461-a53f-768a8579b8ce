// Code generated by sqlc. DO NOT EDIT.
// versions:
//   sqlc v1.29.0
// source: users.sql

package postgres

import (
	"context"
	"time"

	"github.com/jackc/pgx/v5/pgtype"
)

const createUser = `-- name: CreateUser :one
INSERT INTO users (
        identifier,
        approved,
        banned,
        meta_data,
        roles,
        expires_at,
        created_at,
        updated_at
    )
VALUES (
        $1,
        $2,
        $3,
        $4,
        $5,
        $6,
        CURRENT_TIMESTAMP,
        CURRENT_TIMESTAMP
    )
RETURNING id, identifier, approved, banned, meta_data, roles, expires_at, created_at, updated_at, deleted_at
`

type CreateUserParams struct {
	Identifier string             `json:"identifier"`
	Approved   bool               `json:"approved"`
	Banned     bool               `json:"banned"`
	MetaData   []byte             `json:"meta_data"`
	Roles      []string           `json:"roles"`
	ExpiresAt  pgtype.Timestamptz `json:"expires_at"`
}

func (q *Queries) CreateUser(ctx context.Context, arg CreateUserParams) (User, error) {
	row := q.db.QueryRow(ctx, createUser,
		arg.Identifier,
		arg.Approved,
		arg.Banned,
		arg.MetaData,
		arg.Roles,
		arg.ExpiresAt,
	)
	var i User
	err := row.Scan(
		&i.ID,
		&i.Identifier,
		&i.Approved,
		&i.Banned,
		&i.MetaData,
		&i.Roles,
		&i.ExpiresAt,
		&i.CreatedAt,
		&i.UpdatedAt,
		&i.DeletedAt,
	)
	return i, err
}

const getActiveUserById = `-- name: GetActiveUserById :one
SELECT id, identifier, approved, banned, meta_data, roles, expires_at, created_at, updated_at, deleted_at
FROM users
WHERE id = $1
    AND banned = false
    AND expires_at > CURRENT_TIMESTAMP
    AND deleted_at IS NULL
LIMIT 1
`

func (q *Queries) GetActiveUserById(ctx context.Context, id int64) (User, error) {
	row := q.db.QueryRow(ctx, getActiveUserById, id)
	var i User
	err := row.Scan(
		&i.ID,
		&i.Identifier,
		&i.Approved,
		&i.Banned,
		&i.MetaData,
		&i.Roles,
		&i.ExpiresAt,
		&i.CreatedAt,
		&i.UpdatedAt,
		&i.DeletedAt,
	)
	return i, err
}

const getActiveUserByIdentifier = `-- name: GetActiveUserByIdentifier :one
SELECT id, identifier, approved, banned, meta_data, roles, expires_at, created_at, updated_at, deleted_at
FROM users
WHERE identifier = $1
    AND banned = false
    AND expires_at > CURRENT_TIMESTAMP
    AND deleted_at IS NULL
LIMIT 1
`

func (q *Queries) GetActiveUserByIdentifier(ctx context.Context, identifier string) (User, error) {
	row := q.db.QueryRow(ctx, getActiveUserByIdentifier, identifier)
	var i User
	err := row.Scan(
		&i.ID,
		&i.Identifier,
		&i.Approved,
		&i.Banned,
		&i.MetaData,
		&i.Roles,
		&i.ExpiresAt,
		&i.CreatedAt,
		&i.UpdatedAt,
		&i.DeletedAt,
	)
	return i, err
}

const getSafeUserById = `-- name: GetSafeUserById :one
SELECT id,
    identifier,
    approved,
    banned,
    roles
FROM users
WHERE id = $1
    AND deleted_at IS NULL
LIMIT 1
`

type GetSafeUserByIdRow struct {
	ID         int64    `json:"id"`
	Identifier string   `json:"identifier"`
	Approved   bool     `json:"approved"`
	Banned     bool     `json:"banned"`
	Roles      []string `json:"roles"`
}

func (q *Queries) GetSafeUserById(ctx context.Context, id int64) (GetSafeUserByIdRow, error) {
	row := q.db.QueryRow(ctx, getSafeUserById, id)
	var i GetSafeUserByIdRow
	err := row.Scan(
		&i.ID,
		&i.Identifier,
		&i.Approved,
		&i.Banned,
		&i.Roles,
	)
	return i, err
}

const getUserById = `-- name: GetUserById :one
SELECT id, identifier, approved, banned, meta_data, roles, expires_at, created_at, updated_at, deleted_at
FROM users
WHERE id = $1
    AND deleted_at IS NULL
LIMIT 1
`

func (q *Queries) GetUserById(ctx context.Context, id int64) (User, error) {
	row := q.db.QueryRow(ctx, getUserById, id)
	var i User
	err := row.Scan(
		&i.ID,
		&i.Identifier,
		&i.Approved,
		&i.Banned,
		&i.MetaData,
		&i.Roles,
		&i.ExpiresAt,
		&i.CreatedAt,
		&i.UpdatedAt,
		&i.DeletedAt,
	)
	return i, err
}

const getUserByIdentifier = `-- name: GetUserByIdentifier :one
SELECT id, identifier, approved, banned, meta_data, roles, expires_at, created_at, updated_at, deleted_at
FROM users
WHERE identifier = $1
    AND deleted_at IS NULL
LIMIT 1
`

func (q *Queries) GetUserByIdentifier(ctx context.Context, identifier string) (User, error) {
	row := q.db.QueryRow(ctx, getUserByIdentifier, identifier)
	var i User
	err := row.Scan(
		&i.ID,
		&i.Identifier,
		&i.Approved,
		&i.Banned,
		&i.MetaData,
		&i.Roles,
		&i.ExpiresAt,
		&i.CreatedAt,
		&i.UpdatedAt,
		&i.DeletedAt,
	)
	return i, err
}

const listAllUsers = `-- name: ListAllUsers :many
SELECT id, identifier, approved, banned, meta_data, roles, expires_at, created_at, updated_at, deleted_at FROM users
ORDER BY created_at DESC
LIMIT $1 OFFSET $2
`

type ListAllUsersParams struct {
	Limit  int32 `json:"limit"`
	Offset int32 `json:"offset"`
}

func (q *Queries) ListAllUsers(ctx context.Context, arg ListAllUsersParams) ([]User, error) {
	rows, err := q.db.Query(ctx, listAllUsers, arg.Limit, arg.Offset)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	items := []User{}
	for rows.Next() {
		var i User
		if err := rows.Scan(
			&i.ID,
			&i.Identifier,
			&i.Approved,
			&i.Banned,
			&i.MetaData,
			&i.Roles,
			&i.ExpiresAt,
			&i.CreatedAt,
			&i.UpdatedAt,
			&i.DeletedAt,
		); err != nil {
			return nil, err
		}
		items = append(items, i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const listDeletedUsers = `-- name: ListDeletedUsers :many
SELECT id, identifier, approved, banned, meta_data, roles, expires_at, created_at, updated_at, deleted_at FROM users
WHERE deleted_at IS NOT NULL
ORDER BY created_at DESC
LIMIT $1 OFFSET $2
`

type ListDeletedUsersParams struct {
	Limit  int32 `json:"limit"`
	Offset int32 `json:"offset"`
}

func (q *Queries) ListDeletedUsers(ctx context.Context, arg ListDeletedUsersParams) ([]User, error) {
	rows, err := q.db.Query(ctx, listDeletedUsers, arg.Limit, arg.Offset)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	items := []User{}
	for rows.Next() {
		var i User
		if err := rows.Scan(
			&i.ID,
			&i.Identifier,
			&i.Approved,
			&i.Banned,
			&i.MetaData,
			&i.Roles,
			&i.ExpiresAt,
			&i.CreatedAt,
			&i.UpdatedAt,
			&i.DeletedAt,
		); err != nil {
			return nil, err
		}
		items = append(items, i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const listNotDeletedUsers = `-- name: ListNotDeletedUsers :many
SELECT id, identifier, approved, banned, meta_data, roles, expires_at, created_at, updated_at, deleted_at FROM users
WHERE deleted_at IS NULL
ORDER BY created_at DESC
LIMIT $1 OFFSET $2
`

type ListNotDeletedUsersParams struct {
	Limit  int32 `json:"limit"`
	Offset int32 `json:"offset"`
}

func (q *Queries) ListNotDeletedUsers(ctx context.Context, arg ListNotDeletedUsersParams) ([]User, error) {
	rows, err := q.db.Query(ctx, listNotDeletedUsers, arg.Limit, arg.Offset)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	items := []User{}
	for rows.Next() {
		var i User
		if err := rows.Scan(
			&i.ID,
			&i.Identifier,
			&i.Approved,
			&i.Banned,
			&i.MetaData,
			&i.Roles,
			&i.ExpiresAt,
			&i.CreatedAt,
			&i.UpdatedAt,
			&i.DeletedAt,
		); err != nil {
			return nil, err
		}
		items = append(items, i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const listUsersWithProfileAndContact = `-- name: ListUsersWithProfileAndContact :many
SELECT 
    u.id as user_id,
    u.identifier as user_identifier,
    u.approved as user_approved,
    u.banned as user_banned,
    u.meta_data as user_meta_data,
    u.roles as user_roles,
    u.expires_at as user_expires_at,
    u.created_at as user_created_at,
    u.updated_at as user_updated_at,
    c.id as contact_id,
    c.identifier as contact_identifier,
    c.contact_type as contact_type,
    c.mobile as contact_mobile,
    c.email as contact_email,
    c.is_mobile_verified as contact_mobile_verified,
    c.is_email_verified as contact_email_verified,
    c.meta_data as contact_meta_data,
    c.created_at as contact_created_at,
    p.id as profile_id,
    p.identifier as profile_identifier,
    p.profile_type as profile_type,
    p.first_name as profile_first_name,
    p.last_name as profile_last_name,
    p.national_id as profile_national_id,
    p.status as profile_status,
    p.meta_data as profile_meta_data,
    p.created_at as profile_created_at
FROM users u
LEFT JOIN contacts c ON c.user_id = u.id AND c.deleted_at IS NULL
LEFT JOIN profiles p ON p.user_id = u.id AND p.deleted_at IS NULL
WHERE u.deleted_at IS NULL
ORDER BY u.created_at DESC
LIMIT $1 OFFSET $2
`

type ListUsersWithProfileAndContactParams struct {
	Limit  int32 `json:"limit"`
	Offset int32 `json:"offset"`
}

type ListUsersWithProfileAndContactRow struct {
	UserID                int64              `json:"user_id"`
	UserIdentifier        string             `json:"user_identifier"`
	UserApproved          bool               `json:"user_approved"`
	UserBanned            bool               `json:"user_banned"`
	UserMetaData          []byte             `json:"user_meta_data"`
	UserRoles             []string           `json:"user_roles"`
	UserExpiresAt         pgtype.Timestamptz `json:"user_expires_at"`
	UserCreatedAt         time.Time          `json:"user_created_at"`
	UserUpdatedAt         pgtype.Timestamptz `json:"user_updated_at"`
	ContactID             pgtype.Int8        `json:"contact_id"`
	ContactIdentifier     pgtype.Text        `json:"contact_identifier"`
	ContactType           NullContactType    `json:"contact_type"`
	ContactMobile         pgtype.Text        `json:"contact_mobile"`
	ContactEmail          pgtype.Text        `json:"contact_email"`
	ContactMobileVerified pgtype.Bool        `json:"contact_mobile_verified"`
	ContactEmailVerified  pgtype.Bool        `json:"contact_email_verified"`
	ContactMetaData       []byte             `json:"contact_meta_data"`
	ContactCreatedAt      pgtype.Timestamptz `json:"contact_created_at"`
	ProfileID             pgtype.Int8        `json:"profile_id"`
	ProfileIdentifier     pgtype.Text        `json:"profile_identifier"`
	ProfileType           NullProfileType    `json:"profile_type"`
	ProfileFirstName      pgtype.Text        `json:"profile_first_name"`
	ProfileLastName       pgtype.Text        `json:"profile_last_name"`
	ProfileNationalID     pgtype.Text        `json:"profile_national_id"`
	ProfileStatus         NullProfileStatus  `json:"profile_status"`
	ProfileMetaData       []byte             `json:"profile_meta_data"`
	ProfileCreatedAt      pgtype.Timestamptz `json:"profile_created_at"`
}

func (q *Queries) ListUsersWithProfileAndContact(ctx context.Context, arg ListUsersWithProfileAndContactParams) ([]ListUsersWithProfileAndContactRow, error) {
	rows, err := q.db.Query(ctx, listUsersWithProfileAndContact, arg.Limit, arg.Offset)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	items := []ListUsersWithProfileAndContactRow{}
	for rows.Next() {
		var i ListUsersWithProfileAndContactRow
		if err := rows.Scan(
			&i.UserID,
			&i.UserIdentifier,
			&i.UserApproved,
			&i.UserBanned,
			&i.UserMetaData,
			&i.UserRoles,
			&i.UserExpiresAt,
			&i.UserCreatedAt,
			&i.UserUpdatedAt,
			&i.ContactID,
			&i.ContactIdentifier,
			&i.ContactType,
			&i.ContactMobile,
			&i.ContactEmail,
			&i.ContactMobileVerified,
			&i.ContactEmailVerified,
			&i.ContactMetaData,
			&i.ContactCreatedAt,
			&i.ProfileID,
			&i.ProfileIdentifier,
			&i.ProfileType,
			&i.ProfileFirstName,
			&i.ProfileLastName,
			&i.ProfileNationalID,
			&i.ProfileStatus,
			&i.ProfileMetaData,
			&i.ProfileCreatedAt,
		); err != nil {
			return nil, err
		}
		items = append(items, i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const updateUser = `-- name: UpdateUser :one
UPDATE users
SET (
        approved,
        banned,
        meta_data,
        roles,
        expires_at,
        updated_at
    ) = (
        $1,
        $2,
        $3,
        $4,
        $5,
        CURRENT_TIMESTAMP
    )
WHERE id = $6
RETURNING id, identifier, approved, banned, meta_data, roles, expires_at, created_at, updated_at, deleted_at
`

type UpdateUserParams struct {
	Approved  bool               `json:"approved"`
	Banned    bool               `json:"banned"`
	MetaData  []byte             `json:"meta_data"`
	Roles     []string           `json:"roles"`
	ExpiresAt pgtype.Timestamptz `json:"expires_at"`
	ID        int64              `json:"id"`
}

func (q *Queries) UpdateUser(ctx context.Context, arg UpdateUserParams) (User, error) {
	row := q.db.QueryRow(ctx, updateUser,
		arg.Approved,
		arg.Banned,
		arg.MetaData,
		arg.Roles,
		arg.ExpiresAt,
		arg.ID,
	)
	var i User
	err := row.Scan(
		&i.ID,
		&i.Identifier,
		&i.Approved,
		&i.Banned,
		&i.MetaData,
		&i.Roles,
		&i.ExpiresAt,
		&i.CreatedAt,
		&i.UpdatedAt,
		&i.DeletedAt,
	)
	return i, err
}
