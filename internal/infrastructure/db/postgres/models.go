// Code generated by sqlc. DO NOT EDIT.
// versions:
//   sqlc v1.29.0

package postgres

import (
	"database/sql/driver"
	"fmt"
	"time"

	"github.com/jackc/pgx/v5/pgtype"
)

type ContactType string

const (
	ContactTypeEMAIL  ContactType = "EMAIL"
	ContactTypeMOBILE ContactType = "MOBILE"
)

func (e *ContactType) Scan(src interface{}) error {
	switch s := src.(type) {
	case []byte:
		*e = ContactType(s)
	case string:
		*e = ContactType(s)
	default:
		return fmt.Errorf("unsupported scan type for ContactType: %T", src)
	}
	return nil
}

type NullContactType struct {
	ContactType ContactType `json:"contact_type"`
	Valid       bool        `json:"valid"` // Valid is true if ContactType is not NULL
}

// Scan implements the Scanner interface.
func (ns *NullContactType) Scan(value interface{}) error {
	if value == nil {
		ns.ContactType, ns.Valid = "", false
		return nil
	}
	ns.Valid = true
	return ns.ContactType.Scan(value)
}

// Value implements the driver Valuer interface.
func (ns NullContactType) Value() (driver.Value, error) {
	if !ns.Valid {
		return nil, nil
	}
	return string(ns.ContactType), nil
}

type ProfileStatus string

const (
	ProfileStatusPENDING  ProfileStatus = "PENDING"
	ProfileStatusFILLED   ProfileStatus = "FILLED"
	ProfileStatusREJECTED ProfileStatus = "REJECTED"
	ProfileStatusAPPROVED ProfileStatus = "APPROVED"
	ProfileStatusLOCKED   ProfileStatus = "LOCKED"
)

func (e *ProfileStatus) Scan(src interface{}) error {
	switch s := src.(type) {
	case []byte:
		*e = ProfileStatus(s)
	case string:
		*e = ProfileStatus(s)
	default:
		return fmt.Errorf("unsupported scan type for ProfileStatus: %T", src)
	}
	return nil
}

type NullProfileStatus struct {
	ProfileStatus ProfileStatus `json:"profile_status"`
	Valid         bool          `json:"valid"` // Valid is true if ProfileStatus is not NULL
}

// Scan implements the Scanner interface.
func (ns *NullProfileStatus) Scan(value interface{}) error {
	if value == nil {
		ns.ProfileStatus, ns.Valid = "", false
		return nil
	}
	ns.Valid = true
	return ns.ProfileStatus.Scan(value)
}

// Value implements the driver Valuer interface.
func (ns NullProfileStatus) Value() (driver.Value, error) {
	if !ns.Valid {
		return nil, nil
	}
	return string(ns.ProfileStatus), nil
}

type ProfileType string

const (
	ProfileTypeNATURAL ProfileType = "NATURAL"
	ProfileTypeLEGAL   ProfileType = "LEGAL"
)

func (e *ProfileType) Scan(src interface{}) error {
	switch s := src.(type) {
	case []byte:
		*e = ProfileType(s)
	case string:
		*e = ProfileType(s)
	default:
		return fmt.Errorf("unsupported scan type for ProfileType: %T", src)
	}
	return nil
}

type NullProfileType struct {
	ProfileType ProfileType `json:"profile_type"`
	Valid       bool        `json:"valid"` // Valid is true if ProfileType is not NULL
}

// Scan implements the Scanner interface.
func (ns *NullProfileType) Scan(value interface{}) error {
	if value == nil {
		ns.ProfileType, ns.Valid = "", false
		return nil
	}
	ns.Valid = true
	return ns.ProfileType.Scan(value)
}

// Value implements the driver Valuer interface.
func (ns NullProfileType) Value() (driver.Value, error) {
	if !ns.Valid {
		return nil, nil
	}
	return string(ns.ProfileType), nil
}

type Contact struct {
	// contact unique id
	ID int64 `json:"id"`
	// unique external identifier for inter system internal-external identifier separation
	Identifier  string      `json:"identifier"`
	ContactType ContactType `json:"contact_type"`
	// related user id to determining session owner account
	UserID int64 `json:"user_id"`
	// contact primary mobile phone number for authorization use
	Mobile string `json:"mobile"`
	// holds TOTP bcrypted pass code
	MobileTotp pgtype.Text `json:"mobile_totp"`
	// sets to true if user verified his mobile by first time otp verification
	IsMobileVerified bool `json:"is_mobile_verified"`
	// holds by mobile OTP verification code expire time
	MobileTotpExpiresAt pgtype.Timestamptz `json:"mobile_totp_expires_at"`
	// contact primary e-mail address
	Email string `json:"email"`
	// holds TOTP bcrypted pass code
	EmailTotp pgtype.Text `json:"email_totp"`
	// sets to true if user verified his email by first time otp verification
	IsEmailVerified bool `json:"is_email_verified"`
	// holds by e-mail OTP verification code expire time
	EmailTotpExpiresAt pgtype.Timestamptz `json:"email_totp_expires_at"`
	// contact metadatas
	MetaData []byte `json:"meta_data"`
	// when contact was created
	CreatedAt time.Time `json:"created_at"`
	// when contact was updated
	UpdatedAt pgtype.Timestamptz `json:"updated_at"`
	// when contact was deleted
	DeletedAt pgtype.Timestamptz `json:"deleted_at"`
}

type Profile struct {
	// profile unique id
	ID int64 `json:"id"`
	// unique external identifier for inter system internal-external identifier separation
	Identifier string `json:"identifier"`
	// related user id to determining session owner account
	UserID int64 `json:"user_id"`
	// legal or natural person type deffination
	ProfileType ProfileType `json:"profile_type"`
	// user first name
	FirstName string `json:"first_name"`
	// user last name
	LastName string `json:"last_name"`
	// user unique personal national id-code
	NationalID string `json:"national_id"`
	// profile control status
	Status ProfileStatus `json:"status"`
	// profile metadatas
	MetaData []byte `json:"meta_data"`
	// when profile was created
	CreatedAt time.Time `json:"created_at"`
	// when profile was updated
	UpdatedAt pgtype.Timestamptz `json:"updated_at"`
	// when profile was deleted
	DeletedAt pgtype.Timestamptz `json:"deleted_at"`
}

type Session struct {
	// session unique id
	ID int64 `json:"id"`
	// session creation request host name (for SSO use)
	Host string `json:"host"`
	// unique external identifier for inter system internal-external identifier separation
	Identifier string `json:"identifier"`
	// related user id to determining session owner account
	UserID int64 `json:"user_id"`
	// when session expires (only refresh token would updates this field)
	ExpiresIn time.Time `json:"expires_in"`
	// notification provider token for sending notifications by device
	Notification pgtype.Text `json:"notification"`
	// meta data of session like IP, UserAgent etc...
	MetaData []byte `json:"meta_data"`
	// when session created
	CreatedAt time.Time `json:"created_at"`
	// when session deleted
	DeletedAt pgtype.Timestamptz `json:"deleted_at"`
	// when session updated
	UpdatedAt pgtype.Timestamptz `json:"updated_at"`
}

type User struct {
	// user unique id
	ID int64 `json:"id"`
	// unique external identifier for inter system internal-external identifier separation
	Identifier string `json:"identifier"`
	// is user approved or no
	Approved bool `json:"approved"`
	// is user banned or no
	Banned bool `json:"banned"`
	// user metadatas
	MetaData []byte `json:"meta_data"`
	// user assigned roles for permission controls
	Roles []string `json:"roles"`
	// expire time of user, if not sets then user valid for unlimited time
	ExpiresAt pgtype.Timestamptz `json:"expires_at"`
	// when user was created
	CreatedAt time.Time `json:"created_at"`
	// when user was updated
	UpdatedAt pgtype.Timestamptz `json:"updated_at"`
	// when user was deleted
	DeletedAt pgtype.Timestamptz `json:"deleted_at"`
}
