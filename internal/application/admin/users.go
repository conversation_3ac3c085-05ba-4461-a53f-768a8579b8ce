package admin

import (
	"context"

	"github.com/liveutil/admin_service/internal/infrastructure/db/postgres"
	"github.com/liveutil/admin_service/pb"
	"google.golang.org/protobuf/types/known/timestamppb"
)

const (
	BOTH        = "both"
	DELETED     = "deleted"
	NOT_DELETED = "not_deleted"
)

// ListUsers implements pb.AdminServiceServer.
func (s *service) ListUsers(ctx context.Context, req *pb.UsersListRequest) (*pb.UserListResponse, error) {
	result := &pb.UserListResponse{
		Error:   false,
		Message: "success",
		HasMore: false,
		Count:   0,
		Users:   []*pb.User{},
	}

	var users []postgres.User
	var err error

	switch req.DeletedFilter {
	case BOTH:
		list, err = s.repo.ListAllUsers(ctx, postgres.ListAllUsersParams{
			Limit:  req.PageSize,
			Offset: req.PageOffset,
		})
		if err != nil {
			return nil, err
		}
	case DELETED:
		list, err := s.repo.ListDeletedUsers(ctx, postgres.ListDeletedUsersParams{
			Limit:  req.PageSize,
			Offset: req.PageOffset,
		})
		if err != nil {
			return nil, err
		}
	case NOT_DELETED:
		list, err := s.repo.ListNotDeletedUsers(ctx, postgres.ListNotDeletedUsersParams{
			Limit:  req.PageSize,
			Offset: req.PageOffset,
		})
		if err != nil {
			return nil, err
		}
	}

	result.Count = int32(len(users))
	result.HasMore = int32(len(users)) == req.PageSize

	for _, user := range users {
		result.Users = append(result.Users, &pb.User{
			Identifier: user.Identifier,
			Approved:   user.Approved,
			Banned:     user.Banned,
			Roles:      user.Roles,
			CreatedAt:  timestamppb.New(user.CreatedAt),
		})
	}

	return result, nil
}
