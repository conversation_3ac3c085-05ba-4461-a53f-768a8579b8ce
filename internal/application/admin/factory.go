package admin

import (
	kitlog "github.com/go-kit/log"
	"github.com/liveutil/admin_service/internal/config"
	"github.com/liveutil/admin_service/internal/infrastructure/db/postgres"
	"github.com/liveutil/admin_service/pb"
	"github.com/liveutil/go-lib/paseto"
	"github.com/nats-io/nats.go"
	"github.com/redis/go-redis/v9"
)

type AdminServiceOpts struct {
	Repository      postgres.Store
	Config          *config.Configuration
	Redis           *redis.Client
	NATS            *nats.Conn
	Logger          kitlog.Logger
	PASETO          paseto.Maker
	SchemaPath      string
	ApplicationName string
}

// NewAdminService creates a new user service with all middleware layers
func NewAdminService(opts *AdminServiceOpts) (pb.AdminServiceServer, error) {
	// Create base service
	svc := NewService(opts)

	// Add middleware layers
	svc = NewAuthorizationMiddleware(svc)

	return svc, nil
}
