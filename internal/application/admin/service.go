package admin

import (
	kitlog "github.com/go-kit/log"
	"github.com/liveutil/admin_service/internal/config"
	"github.com/liveutil/admin_service/internal/infrastructure/db/postgres"
	"github.com/liveutil/admin_service/pb"
	"github.com/liveutil/go-lib/paseto"
	"github.com/liveutil/go-lib/totp"
	"github.com/nats-io/nats.go"
	"github.com/pquerna/otp"
	"github.com/redis/go-redis/v9"
)

type service struct {
	pb.UnimplementedAdminServiceServer

	repo         postgres.Store
	paseto       paseto.Maker
	config       *config.Configuration
	redis        *redis.Client
	nats         *nats.Conn
	logger       kitlog.Logger
	totp_config  totp.TOTPServiceConfig
	totp_service totp.TOTPService
}

// NewService creates a new instance of the admin service.
func NewService(opts *AdminServiceOpts) pb.AdminServiceServer {
	totpConfig := totp.TOTPServiceConfig{
		Issuer:    opts.Config.Issuer,
		Digits:    6,
		Period:    300,
		Algorithm: otp.AlgorithmSHA1,
		Skew:      1,
	}

	totpService := totp.NewRedisTOTPService(opts.Redis, opts.ApplicationName, totpConfig)

	return &service{
		repo:         opts.Repository,
		config:       opts.Config,
		redis:        opts.Redis,
		paseto:       opts.PASETO,
		nats:         opts.NATS,
		totp_config:  totpConfig,
		totp_service: totpService,
		logger:       opts.Logger,
	}
}
