package admin

import (
	"context"

	"github.com/liveutil/admin_service/pb"
)

type authorizationMiddleware struct {
	pb.UnimplementedAdminServiceServer

	next pb.AdminServiceServer
}

// NewAuthorizationMiddleware returns new authorization layer for pb.AdminServiceServer
func NewAuthorizationMiddleware(service pb.AdminServiceServer) pb.AdminServiceServer {
	return &authorizationMiddleware{
		next: service,
	}
}

// ListUsers implements pb.AdminServiceServer.
func (a *authorizationMiddleware) ListUsers(ctx context.Context, req *pb.UsersListRequest) (*pb.UserListResponse, error) {
	return a.next.ListUsers(ctx, req)
}
