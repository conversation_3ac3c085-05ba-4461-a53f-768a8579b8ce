{"$schema": "http://json-schema.org/draft-07/schema#", "$id": "UsersListRequest", "$protected": true, "title": "UsersListRequest", "description": "Request to list users with deleted filter applied", "type": "object", "required": ["deleted_filter"], "properties": {"deleted_filter": {"type": "string", "description": "deleted users filter condition [deleted, not_deleted, both]", "enum": ["deleted", "not_deleted", "both"]}, "page_size": {"type": "integer", "description": "returned list page size", "minimum": 1, "maximum": 100}, "page_offset": {"type": "integer", "description": "returned list page offset", "minimum": 0}}, "additionalProperties": false}