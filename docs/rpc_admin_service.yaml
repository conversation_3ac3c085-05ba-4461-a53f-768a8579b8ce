{"swagger": "2.0", "info": {"title": "Administration Service API", "description": "Administration Service API for close loop platform monitoring and management", "version": "1.0.0", "contact": {"name": "Administration Service API Support", "email": "<EMAIL>"}}, "tags": [{"name": "AdminService"}], "schemes": ["http", "https"], "consumes": ["application/json"], "produces": ["application/json"], "paths": {"/v1/admin/users/{page_size}/{page_offset}/{deleted_filter}": {"get": {"summary": "List Users", "description": "Get list of users with deleted status filter applied", "operationId": "AdminService_ListUsers", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/pbUserListResponse"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "page_size", "description": "returned list page size", "in": "path", "required": true, "type": "integer", "format": "int32"}, {"name": "page_offset", "description": "returned list page offset", "in": "path", "required": true, "type": "integer", "format": "int32"}, {"name": "deleted_filter", "description": "deleted users filter condition [deleted, not_deleted, both]", "in": "path", "required": true, "type": "string"}], "tags": ["AdminService"], "security": [{"Bearer": []}]}}}, "definitions": {"pbContact": {"type": "object", "properties": {"identifier": {"type": "string", "title": "contact public identifier to hide real database id of contact"}, "mobile": {"type": "string", "title": "contact mobile number"}, "email": {"type": "string", "title": "contact email address"}, "meta_data": {"type": "object", "additionalProperties": {"type": "string"}, "title": "contact meta data"}, "created_at": {"type": "string", "format": "date-time", "title": "contact creation time"}}, "title": "Contact message containing all contact data"}, "pbProfile": {"type": "object", "properties": {"identifier": {"type": "string", "title": "profile public identifier to hide real database id of profile"}, "profile_type": {"type": "string", "title": "profile type"}, "first_name": {"type": "string", "title": "profile first name"}, "last_name": {"type": "string", "title": "profile last name"}, "national_id": {"type": "string", "title": "profile national id"}, "status": {"type": "string", "title": "profile status"}, "meta_data": {"type": "object", "additionalProperties": {"type": "string"}, "title": "profile meta data"}, "created_at": {"type": "string", "format": "date-time", "title": "profile creation time"}}, "title": "Profile message containing all profile data"}, "pbUser": {"type": "object", "properties": {"identifier": {"type": "string", "title": "user public identifier to hide real database id of user"}, "approved": {"type": "boolean", "title": "user approval status"}, "banned": {"type": "boolean", "title": "user ban status"}, "roles": {"type": "array", "items": {"type": "string"}, "title": "user roles"}, "created_at": {"type": "string", "format": "date-time", "title": "user creation time"}, "meta_data": {"type": "object", "additionalProperties": {"type": "string"}, "title": "user meta data"}, "contact": {"$ref": "#/definitions/pbContact", "title": "user associated contact"}, "profile": {"$ref": "#/definitions/pbProfile", "title": "user associated profile"}}, "title": "User message containing all user data and nested entities"}, "pbUserListResponse": {"type": "object", "properties": {"error": {"type": "boolean", "title": "error sets true if processing failed otherwise sets false"}, "message": {"type": "string", "title": "message contains error message if error is true or helper message if error is false"}, "has_more": {"type": "boolean", "title": "has more items"}, "count": {"type": "integer", "format": "int32", "title": "count of items"}, "users": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/pbUser"}, "title": "list of users with pagination"}}, "title": "UserListResponse message for fetching list of users"}, "protobufAny": {"type": "object", "properties": {"@type": {"type": "string"}}, "additionalProperties": {}}, "rpcStatus": {"type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "message": {"type": "string"}, "details": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/protobufAny"}}}}}, "securityDefinitions": {"Bearer": {"type": "<PERSON><PERSON><PERSON><PERSON>", "description": "Authentication token, prefixed by Bear<PERSON>: Bearer <token>", "name": "Authorization", "in": "header"}}, "security": [{"Bearer": []}]}