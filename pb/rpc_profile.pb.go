// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v5.29.3
// source: rpc_profile.proto

package pb

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// Profile message containing all profile data
type Profile struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// profile public identifier to hide real database id of profile
	Identifier string `protobuf:"bytes,1,opt,name=identifier,proto3" json:"identifier,omitempty"`
	// profile type
	ProfileType string `protobuf:"bytes,2,opt,name=profile_type,json=profileType,proto3" json:"profile_type,omitempty"`
	// profile first name
	FirstName string `protobuf:"bytes,3,opt,name=first_name,json=firstName,proto3" json:"first_name,omitempty"`
	// profile last name
	LastName string `protobuf:"bytes,4,opt,name=last_name,json=lastName,proto3" json:"last_name,omitempty"`
	// profile national id
	NationalId string `protobuf:"bytes,5,opt,name=national_id,json=nationalId,proto3" json:"national_id,omitempty"`
	// profile status
	Status string `protobuf:"bytes,6,opt,name=status,proto3" json:"status,omitempty"`
	// profile meta data
	MetaData map[string]string `protobuf:"bytes,7,rep,name=meta_data,json=metaData,proto3" json:"meta_data,omitempty" protobuf_key:"bytes,1,opt,name=key" protobuf_val:"bytes,2,opt,name=value"`
	// profile creation time
	CreatedAt     *timestamppb.Timestamp `protobuf:"bytes,8,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Profile) Reset() {
	*x = Profile{}
	mi := &file_rpc_profile_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Profile) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Profile) ProtoMessage() {}

func (x *Profile) ProtoReflect() protoreflect.Message {
	mi := &file_rpc_profile_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Profile.ProtoReflect.Descriptor instead.
func (*Profile) Descriptor() ([]byte, []int) {
	return file_rpc_profile_proto_rawDescGZIP(), []int{0}
}

func (x *Profile) GetIdentifier() string {
	if x != nil {
		return x.Identifier
	}
	return ""
}

func (x *Profile) GetProfileType() string {
	if x != nil {
		return x.ProfileType
	}
	return ""
}

func (x *Profile) GetFirstName() string {
	if x != nil {
		return x.FirstName
	}
	return ""
}

func (x *Profile) GetLastName() string {
	if x != nil {
		return x.LastName
	}
	return ""
}

func (x *Profile) GetNationalId() string {
	if x != nil {
		return x.NationalId
	}
	return ""
}

func (x *Profile) GetStatus() string {
	if x != nil {
		return x.Status
	}
	return ""
}

func (x *Profile) GetMetaData() map[string]string {
	if x != nil {
		return x.MetaData
	}
	return nil
}

func (x *Profile) GetCreatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.CreatedAt
	}
	return nil
}

var File_rpc_profile_proto protoreflect.FileDescriptor

const file_rpc_profile_proto_rawDesc = "" +
	"\n" +
	"\x11rpc_profile.proto\x12\x02pb\x1a\x1fgoogle/protobuf/timestamp.proto\"\xf1\x02\n" +
	"\aProfile\x12\x1e\n" +
	"\n" +
	"identifier\x18\x01 \x01(\tR\n" +
	"identifier\x12!\n" +
	"\fprofile_type\x18\x02 \x01(\tR\vprofileType\x12\x1d\n" +
	"\n" +
	"first_name\x18\x03 \x01(\tR\tfirstName\x12\x1b\n" +
	"\tlast_name\x18\x04 \x01(\tR\blastName\x12\x1f\n" +
	"\vnational_id\x18\x05 \x01(\tR\n" +
	"nationalId\x12\x16\n" +
	"\x06status\x18\x06 \x01(\tR\x06status\x126\n" +
	"\tmeta_data\x18\a \x03(\v2\x19.pb.Profile.MetaDataEntryR\bmetaData\x129\n" +
	"\n" +
	"created_at\x18\b \x01(\v2\x1a.google.protobuf.TimestampR\tcreatedAt\x1a;\n" +
	"\rMetaDataEntry\x12\x10\n" +
	"\x03key\x18\x01 \x01(\tR\x03key\x12\x14\n" +
	"\x05value\x18\x02 \x01(\tR\x05value:\x028\x01B&Z$github.com/liveutil/admin_service/pbb\x06proto3"

var (
	file_rpc_profile_proto_rawDescOnce sync.Once
	file_rpc_profile_proto_rawDescData []byte
)

func file_rpc_profile_proto_rawDescGZIP() []byte {
	file_rpc_profile_proto_rawDescOnce.Do(func() {
		file_rpc_profile_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_rpc_profile_proto_rawDesc), len(file_rpc_profile_proto_rawDesc)))
	})
	return file_rpc_profile_proto_rawDescData
}

var file_rpc_profile_proto_msgTypes = make([]protoimpl.MessageInfo, 2)
var file_rpc_profile_proto_goTypes = []any{
	(*Profile)(nil),               // 0: pb.Profile
	nil,                           // 1: pb.Profile.MetaDataEntry
	(*timestamppb.Timestamp)(nil), // 2: google.protobuf.Timestamp
}
var file_rpc_profile_proto_depIdxs = []int32{
	1, // 0: pb.Profile.meta_data:type_name -> pb.Profile.MetaDataEntry
	2, // 1: pb.Profile.created_at:type_name -> google.protobuf.Timestamp
	2, // [2:2] is the sub-list for method output_type
	2, // [2:2] is the sub-list for method input_type
	2, // [2:2] is the sub-list for extension type_name
	2, // [2:2] is the sub-list for extension extendee
	0, // [0:2] is the sub-list for field type_name
}

func init() { file_rpc_profile_proto_init() }
func file_rpc_profile_proto_init() {
	if File_rpc_profile_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_rpc_profile_proto_rawDesc), len(file_rpc_profile_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   2,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_rpc_profile_proto_goTypes,
		DependencyIndexes: file_rpc_profile_proto_depIdxs,
		MessageInfos:      file_rpc_profile_proto_msgTypes,
	}.Build()
	File_rpc_profile_proto = out.File
	file_rpc_profile_proto_goTypes = nil
	file_rpc_profile_proto_depIdxs = nil
}
