// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v5.29.3
// source: rpc_contact.proto

package pb

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// Contact message containing all contact data
type Contact struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// contact public identifier to hide real database id of contact
	Identifier string `protobuf:"bytes,1,opt,name=identifier,proto3" json:"identifier,omitempty"`
	// contact mobile number
	Mobile string `protobuf:"bytes,2,opt,name=mobile,proto3" json:"mobile,omitempty"`
	// contact email address
	Email string `protobuf:"bytes,3,opt,name=email,proto3" json:"email,omitempty"`
	// contact meta data
	MetaData map[string]string `protobuf:"bytes,4,rep,name=meta_data,json=metaData,proto3" json:"meta_data,omitempty" protobuf_key:"bytes,1,opt,name=key" protobuf_val:"bytes,2,opt,name=value"`
	// contact creation time
	CreatedAt     *timestamppb.Timestamp `protobuf:"bytes,5,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Contact) Reset() {
	*x = Contact{}
	mi := &file_rpc_contact_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Contact) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Contact) ProtoMessage() {}

func (x *Contact) ProtoReflect() protoreflect.Message {
	mi := &file_rpc_contact_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Contact.ProtoReflect.Descriptor instead.
func (*Contact) Descriptor() ([]byte, []int) {
	return file_rpc_contact_proto_rawDescGZIP(), []int{0}
}

func (x *Contact) GetIdentifier() string {
	if x != nil {
		return x.Identifier
	}
	return ""
}

func (x *Contact) GetMobile() string {
	if x != nil {
		return x.Mobile
	}
	return ""
}

func (x *Contact) GetEmail() string {
	if x != nil {
		return x.Email
	}
	return ""
}

func (x *Contact) GetMetaData() map[string]string {
	if x != nil {
		return x.MetaData
	}
	return nil
}

func (x *Contact) GetCreatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.CreatedAt
	}
	return nil
}

var File_rpc_contact_proto protoreflect.FileDescriptor

const file_rpc_contact_proto_rawDesc = "" +
	"\n" +
	"\x11rpc_contact.proto\x12\x02pb\x1a\x1fgoogle/protobuf/timestamp.proto\"\x87\x02\n" +
	"\aContact\x12\x1e\n" +
	"\n" +
	"identifier\x18\x01 \x01(\tR\n" +
	"identifier\x12\x16\n" +
	"\x06mobile\x18\x02 \x01(\tR\x06mobile\x12\x14\n" +
	"\x05email\x18\x03 \x01(\tR\x05email\x126\n" +
	"\tmeta_data\x18\x04 \x03(\v2\x19.pb.Contact.MetaDataEntryR\bmetaData\x129\n" +
	"\n" +
	"created_at\x18\x05 \x01(\v2\x1a.google.protobuf.TimestampR\tcreatedAt\x1a;\n" +
	"\rMetaDataEntry\x12\x10\n" +
	"\x03key\x18\x01 \x01(\tR\x03key\x12\x14\n" +
	"\x05value\x18\x02 \x01(\tR\x05value:\x028\x01B&Z$github.com/liveutil/admin_service/pbb\x06proto3"

var (
	file_rpc_contact_proto_rawDescOnce sync.Once
	file_rpc_contact_proto_rawDescData []byte
)

func file_rpc_contact_proto_rawDescGZIP() []byte {
	file_rpc_contact_proto_rawDescOnce.Do(func() {
		file_rpc_contact_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_rpc_contact_proto_rawDesc), len(file_rpc_contact_proto_rawDesc)))
	})
	return file_rpc_contact_proto_rawDescData
}

var file_rpc_contact_proto_msgTypes = make([]protoimpl.MessageInfo, 2)
var file_rpc_contact_proto_goTypes = []any{
	(*Contact)(nil),               // 0: pb.Contact
	nil,                           // 1: pb.Contact.MetaDataEntry
	(*timestamppb.Timestamp)(nil), // 2: google.protobuf.Timestamp
}
var file_rpc_contact_proto_depIdxs = []int32{
	1, // 0: pb.Contact.meta_data:type_name -> pb.Contact.MetaDataEntry
	2, // 1: pb.Contact.created_at:type_name -> google.protobuf.Timestamp
	2, // [2:2] is the sub-list for method output_type
	2, // [2:2] is the sub-list for method input_type
	2, // [2:2] is the sub-list for extension type_name
	2, // [2:2] is the sub-list for extension extendee
	0, // [0:2] is the sub-list for field type_name
}

func init() { file_rpc_contact_proto_init() }
func file_rpc_contact_proto_init() {
	if File_rpc_contact_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_rpc_contact_proto_rawDesc), len(file_rpc_contact_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   2,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_rpc_contact_proto_goTypes,
		DependencyIndexes: file_rpc_contact_proto_depIdxs,
		MessageInfos:      file_rpc_contact_proto_msgTypes,
	}.Build()
	File_rpc_contact_proto = out.File
	file_rpc_contact_proto_goTypes = nil
	file_rpc_contact_proto_depIdxs = nil
}
