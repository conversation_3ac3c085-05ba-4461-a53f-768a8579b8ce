// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v5.29.3
// source: rpc_user.proto

package pb

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// User message containing all user data and nested entities
type User struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// user public identifier to hide real database id of user
	Identifier string `protobuf:"bytes,1,opt,name=identifier,proto3" json:"identifier,omitempty"`
	// user approval status
	Approved bool `protobuf:"varint,2,opt,name=approved,proto3" json:"approved,omitempty"`
	// user ban status
	Banned bool `protobuf:"varint,3,opt,name=banned,proto3" json:"banned,omitempty"`
	// user roles
	Roles []string `protobuf:"bytes,4,rep,name=roles,proto3" json:"roles,omitempty"`
	// user creation time
	CreatedAt *timestamppb.Timestamp `protobuf:"bytes,5,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
	// user meta data
	MetaData map[string]string `protobuf:"bytes,6,rep,name=meta_data,json=metaData,proto3" json:"meta_data,omitempty" protobuf_key:"bytes,1,opt,name=key" protobuf_val:"bytes,2,opt,name=value"`
	// user associated contact
	Contact *Contact `protobuf:"bytes,7,opt,name=contact,proto3" json:"contact,omitempty"`
	// user associated profile
	Profile       *Profile `protobuf:"bytes,8,opt,name=profile,proto3" json:"profile,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *User) Reset() {
	*x = User{}
	mi := &file_rpc_user_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *User) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*User) ProtoMessage() {}

func (x *User) ProtoReflect() protoreflect.Message {
	mi := &file_rpc_user_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use User.ProtoReflect.Descriptor instead.
func (*User) Descriptor() ([]byte, []int) {
	return file_rpc_user_proto_rawDescGZIP(), []int{0}
}

func (x *User) GetIdentifier() string {
	if x != nil {
		return x.Identifier
	}
	return ""
}

func (x *User) GetApproved() bool {
	if x != nil {
		return x.Approved
	}
	return false
}

func (x *User) GetBanned() bool {
	if x != nil {
		return x.Banned
	}
	return false
}

func (x *User) GetRoles() []string {
	if x != nil {
		return x.Roles
	}
	return nil
}

func (x *User) GetCreatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.CreatedAt
	}
	return nil
}

func (x *User) GetMetaData() map[string]string {
	if x != nil {
		return x.MetaData
	}
	return nil
}

func (x *User) GetContact() *Contact {
	if x != nil {
		return x.Contact
	}
	return nil
}

func (x *User) GetProfile() *Profile {
	if x != nil {
		return x.Profile
	}
	return nil
}

var File_rpc_user_proto protoreflect.FileDescriptor

const file_rpc_user_proto_rawDesc = "" +
	"\n" +
	"\x0erpc_user.proto\x12\x02pb\x1a\x1fgoogle/protobuf/timestamp.proto\x1a\x11rpc_profile.proto\x1a\x11rpc_contact.proto\"\xeb\x02\n" +
	"\x04User\x12\x1e\n" +
	"\n" +
	"identifier\x18\x01 \x01(\tR\n" +
	"identifier\x12\x1a\n" +
	"\bapproved\x18\x02 \x01(\bR\bapproved\x12\x16\n" +
	"\x06banned\x18\x03 \x01(\bR\x06banned\x12\x14\n" +
	"\x05roles\x18\x04 \x03(\tR\x05roles\x129\n" +
	"\n" +
	"created_at\x18\x05 \x01(\v2\x1a.google.protobuf.TimestampR\tcreatedAt\x123\n" +
	"\tmeta_data\x18\x06 \x03(\v2\x16.pb.User.MetaDataEntryR\bmetaData\x12%\n" +
	"\acontact\x18\a \x01(\v2\v.pb.ContactR\acontact\x12%\n" +
	"\aprofile\x18\b \x01(\v2\v.pb.ProfileR\aprofile\x1a;\n" +
	"\rMetaDataEntry\x12\x10\n" +
	"\x03key\x18\x01 \x01(\tR\x03key\x12\x14\n" +
	"\x05value\x18\x02 \x01(\tR\x05value:\x028\x01B&Z$github.com/liveutil/admin_service/pbb\x06proto3"

var (
	file_rpc_user_proto_rawDescOnce sync.Once
	file_rpc_user_proto_rawDescData []byte
)

func file_rpc_user_proto_rawDescGZIP() []byte {
	file_rpc_user_proto_rawDescOnce.Do(func() {
		file_rpc_user_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_rpc_user_proto_rawDesc), len(file_rpc_user_proto_rawDesc)))
	})
	return file_rpc_user_proto_rawDescData
}

var file_rpc_user_proto_msgTypes = make([]protoimpl.MessageInfo, 2)
var file_rpc_user_proto_goTypes = []any{
	(*User)(nil),                  // 0: pb.User
	nil,                           // 1: pb.User.MetaDataEntry
	(*timestamppb.Timestamp)(nil), // 2: google.protobuf.Timestamp
	(*Contact)(nil),               // 3: pb.Contact
	(*Profile)(nil),               // 4: pb.Profile
}
var file_rpc_user_proto_depIdxs = []int32{
	2, // 0: pb.User.created_at:type_name -> google.protobuf.Timestamp
	1, // 1: pb.User.meta_data:type_name -> pb.User.MetaDataEntry
	3, // 2: pb.User.contact:type_name -> pb.Contact
	4, // 3: pb.User.profile:type_name -> pb.Profile
	4, // [4:4] is the sub-list for method output_type
	4, // [4:4] is the sub-list for method input_type
	4, // [4:4] is the sub-list for extension type_name
	4, // [4:4] is the sub-list for extension extendee
	0, // [0:4] is the sub-list for field type_name
}

func init() { file_rpc_user_proto_init() }
func file_rpc_user_proto_init() {
	if File_rpc_user_proto != nil {
		return
	}
	file_rpc_profile_proto_init()
	file_rpc_contact_proto_init()
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_rpc_user_proto_rawDesc), len(file_rpc_user_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   2,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_rpc_user_proto_goTypes,
		DependencyIndexes: file_rpc_user_proto_depIdxs,
		MessageInfos:      file_rpc_user_proto_msgTypes,
	}.Build()
	File_rpc_user_proto = out.File
	file_rpc_user_proto_goTypes = nil
	file_rpc_user_proto_depIdxs = nil
}
