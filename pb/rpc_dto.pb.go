// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v5.29.3
// source: rpc_dto.proto

package pb

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// Empty message for methods that require no input
type Empty struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Empty) Reset() {
	*x = Empty{}
	mi := &file_rpc_dto_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Empty) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Empty) ProtoMessage() {}

func (x *Empty) ProtoReflect() protoreflect.Message {
	mi := &file_rpc_dto_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Empty.ProtoReflect.Descriptor instead.
func (*Empty) Descriptor() ([]byte, []int) {
	return file_rpc_dto_proto_rawDescGZIP(), []int{0}
}

// UsersListRequest message for fetching list of users with deleted filter applied
type UsersListRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// returned list page size
	PageSize int32 `protobuf:"varint,1,opt,name=page_size,json=pageSize,proto3" json:"page_size,omitempty"`
	// returned list page offset
	PageOffset int32 `protobuf:"varint,2,opt,name=page_offset,json=pageOffset,proto3" json:"page_offset,omitempty"`
	// deleted users filter condition [deleted, not_deleted, both]
	DeletedFilter string `protobuf:"bytes,3,opt,name=deleted_filter,json=deletedFilter,proto3" json:"deleted_filter,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UsersListRequest) Reset() {
	*x = UsersListRequest{}
	mi := &file_rpc_dto_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UsersListRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UsersListRequest) ProtoMessage() {}

func (x *UsersListRequest) ProtoReflect() protoreflect.Message {
	mi := &file_rpc_dto_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UsersListRequest.ProtoReflect.Descriptor instead.
func (*UsersListRequest) Descriptor() ([]byte, []int) {
	return file_rpc_dto_proto_rawDescGZIP(), []int{1}
}

func (x *UsersListRequest) GetPageSize() int32 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

func (x *UsersListRequest) GetPageOffset() int32 {
	if x != nil {
		return x.PageOffset
	}
	return 0
}

func (x *UsersListRequest) GetDeletedFilter() string {
	if x != nil {
		return x.DeletedFilter
	}
	return ""
}

// UserListResponse message for fetching list of users
type UserListResponse struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// error sets true if processing failed otherwise sets false
	Error bool `protobuf:"varint,1,opt,name=error,proto3" json:"error,omitempty"`
	// message contains error message if error is true or helper message if error is false
	Message string `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
	// has more items
	HasMore bool `protobuf:"varint,3,opt,name=has_more,proto3" json:"has_more,omitempty"`
	// count of items
	Count int32 `protobuf:"varint,4,opt,name=count,proto3" json:"count,omitempty"`
	// list of users with pagination
	Users         []*User `protobuf:"bytes,5,rep,name=users,proto3" json:"users,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UserListResponse) Reset() {
	*x = UserListResponse{}
	mi := &file_rpc_dto_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UserListResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UserListResponse) ProtoMessage() {}

func (x *UserListResponse) ProtoReflect() protoreflect.Message {
	mi := &file_rpc_dto_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UserListResponse.ProtoReflect.Descriptor instead.
func (*UserListResponse) Descriptor() ([]byte, []int) {
	return file_rpc_dto_proto_rawDescGZIP(), []int{2}
}

func (x *UserListResponse) GetError() bool {
	if x != nil {
		return x.Error
	}
	return false
}

func (x *UserListResponse) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

func (x *UserListResponse) GetHasMore() bool {
	if x != nil {
		return x.HasMore
	}
	return false
}

func (x *UserListResponse) GetCount() int32 {
	if x != nil {
		return x.Count
	}
	return 0
}

func (x *UserListResponse) GetUsers() []*User {
	if x != nil {
		return x.Users
	}
	return nil
}

var File_rpc_dto_proto protoreflect.FileDescriptor

const file_rpc_dto_proto_rawDesc = "" +
	"\n" +
	"\rrpc_dto.proto\x12\x02pb\x1a\x0erpc_user.proto\"\a\n" +
	"\x05Empty\"w\n" +
	"\x10UsersListRequest\x12\x1b\n" +
	"\tpage_size\x18\x01 \x01(\x05R\bpageSize\x12\x1f\n" +
	"\vpage_offset\x18\x02 \x01(\x05R\n" +
	"pageOffset\x12%\n" +
	"\x0edeleted_filter\x18\x03 \x01(\tR\rdeletedFilter\"\x94\x01\n" +
	"\x10UserListResponse\x12\x14\n" +
	"\x05error\x18\x01 \x01(\bR\x05error\x12\x18\n" +
	"\amessage\x18\x02 \x01(\tR\amessage\x12\x1a\n" +
	"\bhas_more\x18\x03 \x01(\bR\bhas_more\x12\x14\n" +
	"\x05count\x18\x04 \x01(\x05R\x05count\x12\x1e\n" +
	"\x05users\x18\x05 \x03(\v2\b.pb.UserR\x05usersB&Z$github.com/liveutil/admin_service/pbb\x06proto3"

var (
	file_rpc_dto_proto_rawDescOnce sync.Once
	file_rpc_dto_proto_rawDescData []byte
)

func file_rpc_dto_proto_rawDescGZIP() []byte {
	file_rpc_dto_proto_rawDescOnce.Do(func() {
		file_rpc_dto_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_rpc_dto_proto_rawDesc), len(file_rpc_dto_proto_rawDesc)))
	})
	return file_rpc_dto_proto_rawDescData
}

var file_rpc_dto_proto_msgTypes = make([]protoimpl.MessageInfo, 3)
var file_rpc_dto_proto_goTypes = []any{
	(*Empty)(nil),            // 0: pb.Empty
	(*UsersListRequest)(nil), // 1: pb.UsersListRequest
	(*UserListResponse)(nil), // 2: pb.UserListResponse
	(*User)(nil),             // 3: pb.User
}
var file_rpc_dto_proto_depIdxs = []int32{
	3, // 0: pb.UserListResponse.users:type_name -> pb.User
	1, // [1:1] is the sub-list for method output_type
	1, // [1:1] is the sub-list for method input_type
	1, // [1:1] is the sub-list for extension type_name
	1, // [1:1] is the sub-list for extension extendee
	0, // [0:1] is the sub-list for field type_name
}

func init() { file_rpc_dto_proto_init() }
func file_rpc_dto_proto_init() {
	if File_rpc_dto_proto != nil {
		return
	}
	file_rpc_user_proto_init()
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_rpc_dto_proto_rawDesc), len(file_rpc_dto_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   3,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_rpc_dto_proto_goTypes,
		DependencyIndexes: file_rpc_dto_proto_depIdxs,
		MessageInfos:      file_rpc_dto_proto_msgTypes,
	}.Build()
	File_rpc_dto_proto = out.File
	file_rpc_dto_proto_goTypes = nil
	file_rpc_dto_proto_depIdxs = nil
}
