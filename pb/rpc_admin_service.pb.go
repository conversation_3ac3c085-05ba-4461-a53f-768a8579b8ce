// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v5.29.3
// source: rpc_admin_service.proto

package pb

import (
	_ "github.com/grpc-ecosystem/grpc-gateway/v2/protoc-gen-openapiv2/options"
	_ "google.golang.org/genproto/googleapis/api/annotations"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

var File_rpc_admin_service_proto protoreflect.FileDescriptor

const file_rpc_admin_service_proto_rawDesc = "" +
	"\n" +
	"\x17rpc_admin_service.proto\x12\x02pb\x1a\x1cgoogle/api/annotations.proto\x1a.protoc-gen-openapiv2/options/annotations.proto\x1a\rrpc_dto.proto2\xe0\x01\n" +
	"\fAdminService\x12\xcf\x01\n" +
	"\tListUsers\x12\x14.pb.UsersListRequest\x1a\x14.pb.UserListResponse\"\x95\x01\x92AP\x12\n" +
	"List Users\x1a4Get list of users with deleted status filter appliedb\f\n" +
	"\n" +
	"\n" +
	"\x06Bearer\x12\x00\x82\xd3\xe4\x93\x02<\x12:/v1/admin/users/{page_size}/{page_offset}/{deleted_filter}B\xea\x02\x92A\xc0\x02\x12\xac\x01\n" +
	"\x1aAdministration Service API\x12LAdministration Service API for close loop platform monitoring and management\"9\n" +
	"\"Administration Service API Support\x1a\x13liveutil@icloud.com2\x051.0.0*\x02\x01\x022\x10application/json:\x10application/jsonZY\n" +
	"W\n" +
	"\x06Bearer\x12M\b\x02\x128Authentication token, prefixed by Bearer: Bearer <token>\x1a\rAuthorization \x02b\f\n" +
	"\n" +
	"\n" +
	"\x06Bearer\x12\x00Z$github.com/liveutil/admin_service/pbb\x06proto3"

var file_rpc_admin_service_proto_goTypes = []any{
	(*UsersListRequest)(nil), // 0: pb.UsersListRequest
	(*UserListResponse)(nil), // 1: pb.UserListResponse
}
var file_rpc_admin_service_proto_depIdxs = []int32{
	0, // 0: pb.AdminService.ListUsers:input_type -> pb.UsersListRequest
	1, // 1: pb.AdminService.ListUsers:output_type -> pb.UserListResponse
	1, // [1:2] is the sub-list for method output_type
	0, // [0:1] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_rpc_admin_service_proto_init() }
func file_rpc_admin_service_proto_init() {
	if File_rpc_admin_service_proto != nil {
		return
	}
	file_rpc_dto_proto_init()
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_rpc_admin_service_proto_rawDesc), len(file_rpc_admin_service_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   0,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_rpc_admin_service_proto_goTypes,
		DependencyIndexes: file_rpc_admin_service_proto_depIdxs,
	}.Build()
	File_rpc_admin_service_proto = out.File
	file_rpc_admin_service_proto_goTypes = nil
	file_rpc_admin_service_proto_depIdxs = nil
}
